import FalkorDB from 'falkordb';

// FalkorDB configuration
const FALKOR_CONFIG = {
  host: process.env.FALKOR_HOST || 'localhost',
  port: parseInt(process.env.FALKOR_PORT || '6379'),
  password: process.env.FALKOR_PASSWORD,
  username: process.env.FALKOR_USERNAME,
  database: parseInt(process.env.FALKOR_DATABASE || '0'),
};

// Global connection instance
let falkorClient: FalkorDB | null = null;

/**
 * Get or create FalkorDB connection
 */
export async function getFalkorConnection(): Promise<FalkorDB> {
  if (!falkorClient) {
    try {
      falkorClient = new FalkorDB({
        host: FALKOR_CONFIG.host,
        port: FALKOR_CONFIG.port,
        password: FALKOR_CONFIG.password,
        username: FALKOR_CONFIG.username,
        database: FALKOR_CONFIG.database,
      });
      
      // Test the connection
      await falkorClient.ping();
      console.log('FalkorDB connection established successfully');
    } catch (error) {
      console.error('Failed to connect to FalkorDB:', error);
      throw new Error('FalkorDB connection failed');
    }
  }
  
  return falkorClient;
}

/**
 * Close FalkorDB connection
 */
export async function closeFalkorConnection(): Promise<void> {
  if (falkorClient) {
    try {
      await falkorClient.quit();
      falkorClient = null;
      console.log('FalkorDB connection closed');
    } catch (error) {
      console.error('Error closing FalkorDB connection:', error);
    }
  }
}

/**
 * Create a new graph in FalkorDB
 */
export async function createGraph(graphName: string): Promise<boolean> {
  try {
    const client = await getFalkorConnection();
    const graph = client.selectGraph(graphName);
    
    // Create a simple node to initialize the graph
    const query = 'CREATE (n:Graph {name: $name, created_at: $timestamp})';
    const params = {
      name: graphName,
      timestamp: new Date().toISOString()
    };
    
    await graph.query(query, params);
    console.log(`Graph '${graphName}' created successfully`);
    return true;
  } catch (error) {
    console.error(`Error creating graph '${graphName}':`, error);
    throw error;
  }
}

/**
 * Check if a graph exists
 */
export async function graphExists(graphName: string): Promise<boolean> {
  try {
    const client = await getFalkorConnection();
    const graphs = await client.listGraphs();
    return graphs.includes(graphName);
  } catch (error) {
    console.error(`Error checking if graph '${graphName}' exists:`, error);
    return false;
  }
}

/**
 * Delete a graph
 */
export async function deleteGraph(graphName: string): Promise<boolean> {
  try {
    const client = await getFalkorConnection();
    await client.deleteGraph(graphName);
    console.log(`Graph '${graphName}' deleted successfully`);
    return true;
  } catch (error) {
    console.error(`Error deleting graph '${graphName}':`, error);
    throw error;
  }
}

/**
 * Get graph statistics
 */
export async function getGraphStats(graphName: string): Promise<any> {
  try {
    const client = await getFalkorConnection();
    const graph = client.selectGraph(graphName);
    
    const nodeCountQuery = 'MATCH (n) RETURN count(n) as nodeCount';
    const edgeCountQuery = 'MATCH ()-[r]->() RETURN count(r) as edgeCount';
    
    const [nodeResult, edgeResult] = await Promise.all([
      graph.query(nodeCountQuery),
      graph.query(edgeCountQuery)
    ]);
    
    return {
      nodeCount: nodeResult.data[0]?.nodeCount || 0,
      edgeCount: edgeResult.data[0]?.edgeCount || 0,
      graphName
    };
  } catch (error) {
    console.error(`Error getting stats for graph '${graphName}':`, error);
    throw error;
  }
}

/**
 * Execute a custom query on a graph
 */
export async function executeQuery(
  graphName: string, 
  query: string, 
  params: Record<string, any> = {}
): Promise<any> {
  try {
    const client = await getFalkorConnection();
    const graph = client.selectGraph(graphName);
    
    const result = await graph.query(query, params);
    return result;
  } catch (error) {
    console.error(`Error executing query on graph '${graphName}':`, error);
    throw error;
  }
}

/**
 * Health check for FalkorDB connection
 */
export async function healthCheck(): Promise<{ status: string; message: string }> {
  try {
    const client = await getFalkorConnection();
    await client.ping();
    return { status: 'healthy', message: 'FalkorDB connection is working' };
  } catch (error) {
    return { 
      status: 'unhealthy', 
      message: `FalkorDB connection failed: ${error instanceof Error ? error.message : 'Unknown error'}` 
    };
  }
}
