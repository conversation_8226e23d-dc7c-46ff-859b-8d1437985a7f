import { executeQuery } from './falkordb';

// Types for the existing JSON structure
interface JsonRecord {
  id: string;
  fields: {
    ID: number;
    "HTML URL": string;
    Title: string;
    Breadcrumb: string;
    "Is Deepest": string;
    Country: string;
    "Section ID": number;
    "Cleaned Body": string;
  };
}

interface JsonNode {
  id: string;
  country: string;
  summary: string;
  refinedSummary: string;
  children: Record<string, JsonNode>;
  records: JsonRecord[];
}

interface JsonData {
  children: Record<string, JsonNode>;
}

// Types for FalkorDB nodes
interface CategoryNode {
  id: string;
  name: string;
  country: string;
  summary: string;
  refinedSummary: string;
  nodeType: 'category';
  level: number;
  path: string;
}

interface RecordNode {
  id: string;
  recordId: number;
  title: string;
  htmlUrl: string;
  breadcrumb: string;
  isDeepest: boolean;
  country: string;
  sectionId: number;
  cleanedBody: string;
  nodeType: 'record';
}

/**
 * Transform JSON data structure to FalkorDB nodes and relationships
 */
export function transformJsonToGraph(jsonData: JsonData): {
  categoryNodes: CategoryNode[];
  recordNodes: RecordNode[];
  relationships: Array<{ from: string; to: string; type: string }>;
} {
  const categoryNodes: CategoryNode[] = [];
  const recordNodes: RecordNode[] = [];
  const relationships: Array<{ from: string; to: string; type: string }> = [];

  function processNode(
    name: string,
    node: JsonNode,
    level: number = 0,
    parentPath: string = '',
    parentId?: string
  ) {
    const currentPath = parentPath ? `${parentPath}/${name}` : name;
    
    // Create category node
    const categoryNode: CategoryNode = {
      id: node.id,
      name,
      country: node.country,
      summary: node.summary,
      refinedSummary: node.refinedSummary,
      nodeType: 'category',
      level,
      path: currentPath
    };
    
    categoryNodes.push(categoryNode);
    
    // Create relationship to parent if exists
    if (parentId) {
      relationships.push({
        from: parentId,
        to: node.id,
        type: 'HAS_CHILD'
      });
    }
    
    // Process records
    node.records.forEach(record => {
      const recordNode: RecordNode = {
        id: record.id,
        recordId: record.fields.ID,
        title: record.fields.Title,
        htmlUrl: record.fields["HTML URL"],
        breadcrumb: record.fields.Breadcrumb,
        isDeepest: record.fields["Is Deepest"] === "true",
        country: record.fields.Country,
        sectionId: record.fields["Section ID"],
        cleanedBody: record.fields["Cleaned Body"],
        nodeType: 'record'
      };
      
      recordNodes.push(recordNode);
      
      // Create relationship from category to record
      relationships.push({
        from: node.id,
        to: record.id,
        type: 'CONTAINS'
      });
    });
    
    // Process children recursively
    Object.entries(node.children).forEach(([childName, childNode]) => {
      processNode(childName, childNode, level + 1, currentPath, node.id);
    });
  }

  // Process root level nodes
  Object.entries(jsonData.children).forEach(([name, node]) => {
    processNode(name, node);
  });

  return { categoryNodes, recordNodes, relationships };
}

/**
 * Create category nodes in FalkorDB
 */
export async function createCategoryNodes(
  graphName: string,
  categoryNodes: CategoryNode[]
): Promise<void> {
  try {
    for (const node of categoryNodes) {
      const query = `
        CREATE (c:Category {
          id: $id,
          name: $name,
          country: $country,
          summary: $summary,
          refinedSummary: $refinedSummary,
          nodeType: $nodeType,
          level: $level,
          path: $path,
          created_at: $timestamp
        })
      `;
      
      const params = {
        id: node.id,
        name: node.name,
        country: node.country,
        summary: node.summary,
        refinedSummary: node.refinedSummary,
        nodeType: node.nodeType,
        level: node.level,
        path: node.path,
        timestamp: new Date().toISOString()
      };
      
      await executeQuery(graphName, query, params);
    }
    
    console.log(`Created ${categoryNodes.length} category nodes`);
  } catch (error) {
    console.error('Error creating category nodes:', error);
    throw error;
  }
}

/**
 * Create record nodes in FalkorDB
 */
export async function createRecordNodes(
  graphName: string,
  recordNodes: RecordNode[]
): Promise<void> {
  try {
    for (const node of recordNodes) {
      const query = `
        CREATE (r:Record {
          id: $id,
          recordId: $recordId,
          title: $title,
          htmlUrl: $htmlUrl,
          breadcrumb: $breadcrumb,
          isDeepest: $isDeepest,
          country: $country,
          sectionId: $sectionId,
          cleanedBody: $cleanedBody,
          nodeType: $nodeType,
          created_at: $timestamp
        })
      `;
      
      const params = {
        id: node.id,
        recordId: node.recordId,
        title: node.title,
        htmlUrl: node.htmlUrl,
        breadcrumb: node.breadcrumb,
        isDeepest: node.isDeepest,
        country: node.country,
        sectionId: node.sectionId,
        cleanedBody: node.cleanedBody,
        nodeType: node.nodeType,
        timestamp: new Date().toISOString()
      };
      
      await executeQuery(graphName, query, params);
    }
    
    console.log(`Created ${recordNodes.length} record nodes`);
  } catch (error) {
    console.error('Error creating record nodes:', error);
    throw error;
  }
}

/**
 * Create relationships in FalkorDB
 */
export async function createRelationships(
  graphName: string,
  relationships: Array<{ from: string; to: string; type: string }>
): Promise<void> {
  try {
    for (const rel of relationships) {
      const query = `
        MATCH (from), (to)
        WHERE from.id = $fromId AND to.id = $toId
        CREATE (from)-[:${rel.type} {created_at: $timestamp}]->(to)
      `;
      
      const params = {
        fromId: rel.from,
        toId: rel.to,
        timestamp: new Date().toISOString()
      };
      
      await executeQuery(graphName, query, params);
    }
    
    console.log(`Created ${relationships.length} relationships`);
  } catch (error) {
    console.error('Error creating relationships:', error);
    throw error;
  }
}

/**
 * Upsert a single category node
 */
export async function upsertCategoryNode(
  graphName: string,
  node: CategoryNode
): Promise<void> {
  try {
    const query = `
      MERGE (c:Category {id: $id})
      SET c.name = $name,
          c.country = $country,
          c.summary = $summary,
          c.refinedSummary = $refinedSummary,
          c.nodeType = $nodeType,
          c.level = $level,
          c.path = $path,
          c.updated_at = $timestamp
      ON CREATE SET c.created_at = $timestamp
    `;
    
    const params = {
      id: node.id,
      name: node.name,
      country: node.country,
      summary: node.summary,
      refinedSummary: node.refinedSummary,
      nodeType: node.nodeType,
      level: node.level,
      path: node.path,
      timestamp: new Date().toISOString()
    };
    
    await executeQuery(graphName, query, params);
    console.log(`Upserted category node: ${node.name}`);
  } catch (error) {
    console.error('Error upserting category node:', error);
    throw error;
  }
}

/**
 * Upsert a single record node
 */
export async function upsertRecordNode(
  graphName: string,
  node: RecordNode
): Promise<void> {
  try {
    const query = `
      MERGE (r:Record {id: $id})
      SET r.recordId = $recordId,
          r.title = $title,
          r.htmlUrl = $htmlUrl,
          r.breadcrumb = $breadcrumb,
          r.isDeepest = $isDeepest,
          r.country = $country,
          r.sectionId = $sectionId,
          r.cleanedBody = $cleanedBody,
          r.nodeType = $nodeType,
          r.updated_at = $timestamp
      ON CREATE SET r.created_at = $timestamp
    `;
    
    const params = {
      id: node.id,
      recordId: node.recordId,
      title: node.title,
      htmlUrl: node.htmlUrl,
      breadcrumb: node.breadcrumb,
      isDeepest: node.isDeepest,
      country: node.country,
      sectionId: node.sectionId,
      cleanedBody: node.cleanedBody,
      nodeType: node.nodeType,
      timestamp: new Date().toISOString()
    };
    
    await executeQuery(graphName, query, params);
    console.log(`Upserted record node: ${node.title}`);
  } catch (error) {
    console.error('Error upserting record node:', error);
    throw error;
  }
}
